<?php

namespace App\Console\Commands\Data;
use App\Models\StoreLeads\Domain;
use App\Models\StoreLeads\Platform;
use App\Models\StoreLeads\PlatformHost;
use App\Services\DomainNormalizationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SlExtractPlatformHosts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:sl-extract-platform-hosts {id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Extract platform hosts from domains';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $id = $this->argument('id')?? 0;
        $domainNormalizer = app(DomainNormalizationService::class);

        Log::info('extract-platform-hosts: Start at id: '.$id);

        Domain::where('name', 'not like', 'www.%') // not starting with www
            ->whereRaw('CHAR_LENGTH(name) - CHAR_LENGTH(REPLACE(name, ".", "")) >= 2') // only if it has at least 2 dots
            ->where('id', '>', $id)
            ->chunk(1000, function ($domains) use ($domainNormalizer) {
                foreach ($domains as $domain) {
                    $url = urlTrim($domain->name); //remove http and www

                    try {
                        $normalized = $domainNormalizer->parseAndNormalize($url);
                        $root = $normalized['root_domain'];
                    } catch (\Throwable $e) {
                        Log::error('extract-platform-hosts: Failed on domain: '.$domain->name.' - '.$e->getMessage());
                        continue;
                    }

                    if (empty($root)) {
                        continue;
                    }

                    // If already inserted in platform hosts, then skip
                    if (PlatformHost::where('registrable', $root)->exists()) {
                        continue;
                    }

                    $domainCount = Domain::where('name', 'like', '%'.$root)->count();
                    if ($domainCount > 10) {
                        // Calculate confidence based on the number of domains (how confident we are that this is a platform)
                        if ($domainCount > 250) {
                            $platformConfidence = 100;
                        } else if ($domainCount > 150) {
                            $platformConfidence = 90;
                        } else if ($domainCount > 100) {
                            $platformConfidence = 80;
                        } else if ($domainCount > 50) {
                            $platformConfidence = 70;
                        } else {
                            $platformConfidence = 0;
                        }

                        // Insert root domain into platforms to check later
                        PlatformHost::firstOrCreate([
                            'registrable' => $root,
                            'count_subdomains' => $domainCount,
                            'confidence' => $platformConfidence,
                            'is_platform' => false, // initially set to false, will be updated later after manually checking the platforms
                        ]);
                    }

                }
                Log::info('extract-platform-hosts: Processed id: '.$domains->last()->id);
                info('extract-platform-hosts: Processed id:' . $domains->last()->id);
            });
    }
}
